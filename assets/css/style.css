@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap");

* {
  padding: 0;
  margin: 0;
  list-style: none;
  text-decoration: none;
  box-sizing: border-box;
  font-family: "<PERSON><PERSON>", <PERSON><PERSON>, "Helvetica Neue", Helvetica, sans-serif;
}

input,
button {
  outline: none;
  border: none;
}

button {
  cursor: pointer;
  border: 0;
}


/* products__list
.products__list{
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.products__item{
  display: flex;
  flex-direction: column;
  width: calc(25% - 30px * 3);
} */

/* deal */
.deal {
  background-image: url("../images/deal/bgr.png");
  height: 650px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.deal__wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  padding-left: 10%;
}

.deal__tag {
  font-size: 14px;
  text-transform: uppercase;
  color: #ffffff;
  font-weight: 700;
  font-family: "Jost";
}

.deal__title {
  font-size: 48px;
  color: #ffffff;
  font-weight: 700;
  font-family: "Jost";
}

.deal__time {
  display: flex;
  gap: 32px;
}

.deal__comp {
  display: flex;
  flex-direction: column;
}

.deal__comp span:nth-child(1) {
  font-size: 24px;
  color: #ffffff;
  font-weight: 700;
  font-family: "Jost";
  text-align: center;
}

.deal__comp span:nth-child(2) {
  font-size: 10px;
  text-transform: uppercase;
  color: #ffffff;
  font-weight: 700;
  font-family: "Jost";
  text-align: center;

}


/* news */
.news {
  display: flex;
  padding: 15px;
}

.new__wrapper {
  max-width: 1358px;
  width: 100%;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  padding: 73px 0;
}


.products__title,
.new__title {
  font-size: 40px;
  color: #151515;
  font-weight: 500;
}

.new__list {
  display: flex;
  gap: 30px;

  margin-top: 35px;
}

.new__items {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.new__items__type {
  font-size: 10px;
  line-height: 28px;
  text-transform: uppercase;
  color: #151515;
  font-weight: 700;
  margin-top: 30px;
}

.new__items__title {
  width: 422px;
  height: 62px;
  font-size: 24px;
  line-height: 36px;
  color: #111111;
  font-weight: 500;
  margin-top: 0;
}

.new__items__descriptions {
  width: 422px;
  height: 47px;
  font-size: 15px;
  line-height: 22px;
  color: #454545;
  margin-top: 15px;
}

.new__items__time {
  font-size: 10px;
  text-transform: uppercase;
  color: #959595;
  font-weight: 700;
  margin-top: 34px;
}

/* footer */
.footer {
  display: flex;
  background-color: #151515;
  color: #fff;
}

.footer__wrapper {
  display: flex;
  flex-direction: column;
  max-width: 1358px;
  width: 100%;
  margin: 0 auto;
  padding-top: 100px;
}

.footer_top {
  display: flex;
  padding-bottom: 95px;
}

.footer__cols {
  display: flex;
  flex-direction: column;
}


.footer__cols:nth-child(1),
.footer__cols:nth-child(2) {
  flex: 3;
}


.footer__cols:nth-child(3) {
  flex: 6;
}

.footer__title {
  font-size: 16px;
  text-transform: uppercase;
  color: #ffffff;
  font-weight: 700;
}

.footer__cols__join,
.footer__list {

  margin-top: 32px;
}

.footer__cols__join,
.footer__items a {
  font-size: 13px;
  color: #858585;
  font-weight: 500;
}

.footer__form {
  display: flex;
  justify-content: space-between;
  border-bottom: 2px solid #858585;
  margin-top: 38px;
}

.footer__form__input {
  background-color: transparent;
}

.footer__form__btn {
  background-color: unset;
  font-size: 13px;
  color: #858585;
  font-weight: 500;
  padding: 0 13px;
}

.footer__notifi {
  font-size: 13px;
  color: #858585;
  font-weight: 500;
  margin-top: 22px;
}


/* footer_bottom */
.footer__center {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #2d2d2d;
  padding: 38px 0;
}

.footer__center__social {
  display: flex;
  gap: 19px;
  color: #858585;
}

.footer__items {
  transition: all .2s ease-in-out;
}

.footer__items:hover {
  opacity: 0.8;
  transform: translateX(4px);
}

.footer_bottom__nav a {
  font-size: 13px;
  color: #858585;
  font-weight: 700;
  font-family: "Jost";
}



/* footer__copyright */
.footer__bottom {
  display: flex;
  justify-content: space-between;
  padding-bottom: 17px;
  padding-top: 18px;
}

.footer__bottom__copy {
  font-size: 13px;
  color: #858585;
  font-weight: 500;
}

.footer__bottom__pay {
  object-fit: contain;
}


@media (max-width: 1358px) {
  .footer__wrapper {
    margin: 0 15px;
  }
}