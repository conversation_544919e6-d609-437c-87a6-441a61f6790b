# 🏆 PSD2WEB Challenge 2025 - Team Dev Vibe

## 🎉 Giải Nhất Cuộc Thi PSD2WEB - FPT Polytechnic Thái Nguyên

Dự án **Zonex Fashion Website** được phát triển bởi team **Dev Vibe** trong cuộc thi PSD2WEB do FPT Polytechnic Thái Nguyên tổ chức. Với thời gian thi đấu chỉ **90 phút**, team đã xuất sắc hoàn thành và giành được **🥇 Giải Nhất**.

## 📋 Thông Tin Dự Án

- **Tên dự án**: Zonex Fashion Website
- **Team**: Dev Vibe
- **Thời gian hoàn thành**: 90 phút
- **Kết quả**: 🏆 Giải Nhất
- **Cuộc thi**: PSD2WEB Challenge 2025
- **Đơn vị tổ chức**: FPT Polytechnic Thái Nguyên

## 🎯 Mô Tả Dự Án

Zonex là một website thương mại điện tử chuyên về thời trang, đư<PERSON><PERSON> thiết kế với giao diện hiện đại và thân thiện với người dùng. Website bao gồm các tính năng chính:

### ✨ Tính Năng Chính

- **🏠 Trang chủ hiện đại**: Giao diện đẹp mắt với slider banner tự động
- **🛍️ Danh mục sản phẩm**: Hiển thị các bộ sưu tập thời trang đa dạng
- **🎯 Deal của ngày**: Countdown timer cho các ưu đãi đặc biệt
- **📰 Tin tức mới nhất**: Blog về thời trang và nhiếp ảnh
- **📱 Responsive Design**: Tương thích với mọi thiết bị
- **🛒 Giỏ hàng**: Tính năng mua sắm trực tuyến

### 🎨 Thiết Kế

- **Phong cách**: Modern, Clean, Minimalist
- **Màu sắc**: Tông màu trắng chủ đạo với điểm nhấn
- **Typography**: Font chữ hiện đại, dễ đọc
- **Layout**: Grid system linh hoạt, responsive

## 🛠️ Công Nghệ Sử Dụng

### Frontend
- **HTML5**: Cấu trúc semantic và accessibility
- **CSS3**:
  - Flexbox & Grid Layout
  - CSS Variables
  - Responsive Design
  - Animations & Transitions
- **JavaScript (Vanilla)**:
  - Slider tự động
  - Interactive UI components
  - DOM manipulation

### Thư Viện & Framework
- **Font Awesome 6.7.2**: Icons và biểu tượng
- **Custom CSS Architecture**: Modular CSS structure

## 📁 Cấu Trúc Dự Án

```
psd2web_Dev Vibe/
├── index.html              # Trang chủ chính
├── README.MD               # Tài liệu dự án
└── assets/
    ├── css/
    │   ├── style.css       # CSS chính
    │   ├── header.css      # Styles cho header
    │   ├── main.css        # Styles cho nội dung chính
    │   └── footer.css      # Styles cho footer
    ├── js/
    │   ├── slider.js       # Chức năng slider
    │   └── animation.js    # Animations
    ├── images/
    │   ├── banner/         # Hình ảnh banner
    │   ├── collection/     # Hình ảnh bộ sưu tập
    │   ├── news/           # Hình ảnh tin tức
    │   ├── icons/          # Icons
    │   ├── logo/           # Logo
    │   └── footer/         # Hình ảnh footer
    └── psd/
        └── PSD2WEB_CHALLENGE_2025.psd  # File PSD gốc
```

## 🚀 Tính Năng Nổi Bật

### 1. **Slider Banner Tự Động**
- Chuyển đổi slide tự động mỗi 3 giây
- Dots navigation
- Responsive trên mọi thiết bị
- Smooth transitions

### 2. **Navigation Menu**
- Menu ngang với logo ở giữa
- Hover effects
- Active state indication
- Mobile-friendly

### 3. **Product Collections**
- Grid layout linh hoạt
- Hover effects trên images
- Call-to-action buttons
- Category organization

### 4. **Deal Section**
- Countdown timer
- Eye-catching design
- Promotional content

### 5. **News Section**
- Blog-style layout
- Image thumbnails
- Category tags
- Publication dates

### 6. **Footer**
- Multi-column layout
- Newsletter subscription
- Social media links
- Payment methods display

## 💻 Cách Chạy Dự Án

### Yêu Cầu Hệ Thống
- Web browser hiện đại (Chrome, Firefox, Safari, Edge)
- Không cần server backend

### Hướng Dẫn Cài Đặt

1. **Clone hoặc download dự án**
   ```bash
   git clone [repository-url]
   cd psd2web_Dev Vibe
   ```

2. **Mở file index.html**
   - Cách 1: Double-click vào file `index.html`
   - Cách 2: Sử dụng Live Server extension trong VS Code
   - Cách 3: Chạy local server:
     ```bash
     # Python 3
     python -m http.server 8000

     # Node.js
     npx serve .
     ```

3. **Truy cập website**
   - Mở browser và truy cập: `http://localhost:8000`

## 🎨 Design System

### Colors
- **Primary**: White (#FFFFFF)
- **Secondary**: Black (#000000)
- **Accent**: Various fashion colors
- **Text**: Dark gray tones

### Typography
- **Headings**: Bold, modern fonts
- **Body**: Clean, readable fonts
- **Buttons**: Uppercase, bold styling

### Components
- **Buttons**: Rounded corners, hover effects
- **Cards**: Clean shadows, hover animations
- **Forms**: Modern input styling
- **Navigation**: Horizontal layout with center logo

## 📱 Responsive Design

Website được thiết kế responsive cho:
- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px
- **Mobile**: < 768px

## 🏆 Thành Tích

- **🥇 Giải Nhất** cuộc thi PSD2WEB Challenge 2025
- **⏱️ Hoàn thành trong 90 phút** - thể hiện kỹ năng coding nhanh và chính xác
- **🎯 Pixel-perfect** - chuyển đổi PSD sang HTML/CSS với độ chính xác cao
- **📱 Responsive** - tương thích đa thiết bị

## 👥 Team Dev Vibe

Team Dev Vibe là một nhóm các developer trẻ, năng động với passion về web development và UI/UX design. Với tinh thần làm việc nhóm tốt và kỹ năng technical vững vàng, team đã xuất sắc hoàn thành thử thách trong thời gian ngắn.

### Kỹ Năng Chính
- **Frontend Development**: HTML5, CSS3, JavaScript
- **Responsive Design**: Mobile-first approach
- **UI/UX Implementation**: Pixel-perfect conversion
- **Performance Optimization**: Fast loading, smooth animations
- **Cross-browser Compatibility**: Tương thích đa trình duyệt

## 🔧 Tối Ưu Hóa

### Performance
- **Optimized Images**: Compressed và properly sized
- **Minified CSS**: Clean và organized code
- **Efficient JavaScript**: Vanilla JS cho performance tốt nhất
- **Fast Loading**: Minimal dependencies

### SEO
- **Semantic HTML**: Proper heading structure
- **Meta Tags**: Optimized for search engines
- **Alt Attributes**: Accessible images
- **Clean URLs**: SEO-friendly structure

## 📞 Liên Hệ

Để biết thêm thông tin về dự án hoặc team Dev Vibe, vui lòng liên hệ qua:

- **Email**: [team-email]
- **GitHub**: [github-profile]
- **LinkedIn**: [linkedin-profile]

---

**© 2025 Team Dev Vibe - FPT Polytechnic Thái Nguyên**

*Dự án được phát triển trong khuôn khổ cuộc thi PSD2WEB Challenge 2025*